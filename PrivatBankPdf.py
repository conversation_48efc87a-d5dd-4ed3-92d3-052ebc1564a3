# -*- coding: utf-8 -*-
# pyinstaller --onefile --windowed --name PrivatBankDownloader PrivatBankPdfGUI.py
# https://gemini.google.com/app/2043f90d1b17f244
# Выгрузка квитанций ПриватБанка в PDF БЕЗ GUI
import asyncio
import os
from datetime import datetime
from collections import defaultdict
import aiohttp
import aiofiles
import pandas as pd
from dotenv import load_dotenv
from dateutil.parser import parse

# Загружаем переменные окружения из файла .env
load_dotenv()

# --- КОНФИГУРАЦИЯ ---
# Базовые URL для API ПриватБанка
TRANSACTIONS_URL = "https://acp.privatbank.ua/api/statements/transactions"
RECEIPT_URL = "https://acp.privatbank.ua/api/paysheets/print_receipt"
# Максимальное количество одновременных загрузок PDF
MAX_CONCURRENT_DOWNLOADS = 10
# ВАЖНО: Укажите правильное имя поля для суммы транзакции из API
# Распространенные варианты: "AMT", "SUM", "amount"
AMOUNT_FIELD = "AMT"

async def get_all_transactions(session, token, start_date, end_date):
    """
    Асинхронно получает полный список транзакций за указанный период,
    автоматически обрабатывая пагинацию.
    """
    all_transactions = []
    follow_id = None
    page_num = 1
    headers = {'user-agent': 'Avtoklient', 'token': token, 'Content-Type': 'application/json;charset=utf-8'}
    print(f"Получение транзакций с {start_date} по {end_date}...")
    while True:
        params = {'startDate': start_date, 'endDate': end_date, 'limit': 500}
        if follow_id:
            params['followId'] = follow_id
        try:
            async with session.post(TRANSACTIONS_URL, params=params, headers=headers, timeout=30) as response:
                response.raise_for_status()
                data = await response.json()
                transactions_on_page = data.get('transactions', [])
                if transactions_on_page:
                    all_transactions.extend(transactions_on_page)
                    print(f"  - Получено {len(transactions_on_page)} транзакций со страницы {page_num}")
                if data.get('exist_next_page'):
                    follow_id = data.get('next_page_id')
                    page_num += 1
                else:
                    break
        except aiohttp.ClientError as e:
            print(f"Ошибка при получении транзакций: {e}")
            break
        except Exception as e:
            print(f"Неожиданная ошибка при обработке транзакций: {e}")
            break
    return all_transactions

async def download_receipt(session, transaction, token, output_dir, semaphore):
    """
    Асинхронно загружает PDF-квитанцию для одной транзакции.
    """
    async with semaphore:
        try:
            doc_num = transaction.get('NUM_DOC', '0')
            doc_date_str = transaction.get('DAT_OD', 'UNKNOWN_DATE')
            doc_datetime = parse(doc_date_str, dayfirst=True)
            
            month_folder_name = doc_datetime.strftime('%Y%m')
            month_output_dir = os.path.join(output_dir, month_folder_name)
            os.makedirs(month_output_dir, exist_ok=True)

            doc_date_formatted = doc_datetime.strftime('%d %m %Y')
            filename = f"{doc_num} {doc_date_formatted}.pdf"
            filepath = os.path.join(month_output_dir, filename)

            if os.path.exists(filepath):
                print(f"-> Файл {filename} уже существует, пропуск.")
                return (month_output_dir, filename)

            print(f"-> Загрузка квитанции {filename} в {month_output_dir}...")
            headers = {'token': token, 'Content-Type': 'application/json', 'Accept': 'application/octet-stream'}
            payload = {
                "transactions": [{"account": transaction.get('AUT_MY_ACC'), "reference": transaction.get('REF'), "refn": transaction.get('REFN')}],
                "perPage": 1
            }
            async with session.post(RECEIPT_URL, json=payload, headers=headers, timeout=60) as response:
                if response.status == 200:
                    content = await response.read()
                    async with aiofiles.open(filepath, 'wb') as f:
                        await f.write(content)
                    print(f"   - Файл {filename} успешно сохранен.")
                    return (month_output_dir, filename)
                else:
                    error_text = await response.text()
                    print(f"   - Ошибка {response.status} при загрузке {filename}: {error_text[:200]}")
                    return None
        except Exception as e:
            print(f"   - Не удалось обработать транзакцию {transaction.get('REF')}: {e}")
            return None

async def write_log_files(file_map):
    """Записывает собранные имена файлов в лог-файлы по месяцам."""
    for folder_path, filenames in file_map.items():
        if not filenames:
            continue
        month_str = os.path.basename(folder_path)
        log_filepath = os.path.join(folder_path, f"{month_str}.log")
        filenames.sort()
        log_content = ",".join(filenames)
        async with aiofiles.open(log_filepath, 'w', encoding='utf-8') as f:
            await f.write(log_content)
        print(f"Создан/обновлен лог-файл: {log_filepath}")

def create_excel_report(report_data, base_output_dir, start_date, end_date, partner_code=None):
    """Создает Excel отчет на основе данных транзакций."""
    if not report_data:
        print("Нет данных для создания Excel отчета.")
        return
    try:
        report_data.sort(key=lambda x: x['дата'])
        df = pd.DataFrame(report_data)
        df['дата'] = pd.to_datetime(df['дата']).dt.strftime('%d.%m.%Y')
        
        if partner_code:
            excel_filename = f"Отчет_{partner_code}_{start_date}_по_{end_date}.xlsx"
        else:
            excel_filename = f"Сводный_отчет_{start_date}_по_{end_date}.xlsx"
            
        excel_filepath = os.path.join(base_output_dir, excel_filename)
        df.to_excel(excel_filepath, index=False, engine='openpyxl')
        print(f"\nОтчет Excel успешно сохранен: {excel_filepath}")
    except Exception as e:
        print(f"\nНе удалось создать Excel отчет: {e}")

async def main():
    """
    Главная функция: определяет параметры, получает транзакции, фильтрует их,
    запускает загрузку и создает отчеты.
    """
    # --- УКАЖИТЕ ВАШИ ДАННЫЕ ЗДЕСЬ ---
    # Оставьте пустым ("") или None для обработки ВСЕХ контрагентов
    partner_code = None  # "43028967" 
    token = os.getenv("PB_PPK_TOKEN")
    start_date = "01-01-2024"
    end_date = datetime.today().strftime("%d-%m-%Y")
    # ------------------------------------

    if not token:
        print("Ошибка: токен не найден. Проверьте файл .env и имя переменной PB_PPK_TOKEN.")
        return

    process_all_clients = not partner_code
    client_name = ""
    base_output_dir = ""

    async with aiohttp.ClientSession() as session:
        all_transactions = await get_all_transactions(session, token, start_date, end_date)
        if not all_transactions:
            print("Транзакции за указанный период не найдены.")
            return

        # --- Фильтрация транзакций и сбор данных ---
        transactions_to_process = []
        report_data = []
        client_name_map = {} # Словарь для хранения {partner_code: client_name}

        for tx in all_transactions:
            tx_partner_code = tx.get('AUT_CNTR_CRF')
            if not tx_partner_code:
                continue

            # Сохраняем имя клиента, чтобы не искать его каждый раз
            if tx_partner_code not in client_name_map:
                client_name_map[tx_partner_code] = tx.get('AUT_CNTR_NAM', f'Unknown_{tx_partner_code}')

            # Определяем, нужно ли обрабатывать эту транзакцию
            if process_all_clients or tx_partner_code == partner_code:
                transactions_to_process.append(tx)
                
                # Собираем данные для Excel отчета
                try:
                    doc_datetime = parse(tx.get('DAT_OD', ''), dayfirst=True)
                    amount = float(tx.get(AMOUNT_FIELD, 0.0))
                    if tx.get('TRANTYPE') == 'D':
                        amount = -amount
                    
                    report_data.append({
                        "Клиент": client_name_map[tx_partner_code],
                        "дата": doc_datetime.strftime('%Y-%m-%d'),
                        "сумма": amount
                    })
                except (ValueError, TypeError) as e:
                    print(f" - Предупреждение: не удалось обработать данные для отчета из транзакции {tx.get('REF')}: {e}")

        if not transactions_to_process:
            if not process_all_clients:
                print(f"Транзакции для контрагента с кодом {partner_code} не найдены.")
            else:
                print("Не найдено транзакций для обработки.")
            return

        # --- Определение корневой папки ---
        if process_all_clients:
            base_output_dir = "receipts_all_clients"
            print(f"Режим обработки ВСЕХ клиентов. Квитанции будут сохранены в: {base_output_dir}")
        else:
            client_name = client_name_map.get(partner_code, f"Unknown_{partner_code}")
            base_output_dir = f"{partner_code}_{client_name}"
            print(f"Режим обработки одного клиента: {client_name} ({partner_code})")
            print(f"Квитанции будут сохранены в: {base_output_dir}")
        os.makedirs(base_output_dir, exist_ok=True)

        # --- Запуск загрузок ---
        semaphore = asyncio.Semaphore(MAX_CONCURRENT_DOWNLOADS)
        log_file_map = defaultdict(list)
        download_tasks = []

        for tx in transactions_to_process:
            tx_partner_code = tx.get('AUT_CNTR_CRF')
            tx_client_name = client_name_map.get(tx_partner_code)
            
            # Определяем папку для квитанции (входящие/исходящие)
            direction = "входящие" if tx.get('TRANTYPE') == 'C' else "исходящие"
            
            # Определяем путь для сохранения
            if process_all_clients:
                client_specific_dir = os.path.join(base_output_dir, f"{tx_partner_code}_{tx_client_name}", direction)
            else:
                client_specific_dir = os.path.join(base_output_dir, direction)
            
            os.makedirs(client_specific_dir, exist_ok=True)
            
            task = download_receipt(session, tx, token, client_specific_dir, semaphore)
            download_tasks.append(task)

        print(f"\nНайдено {len(transactions_to_process)} транзакций для загрузки. Начало...")
        results = await asyncio.gather(*download_tasks)
        for result in results:
            if result:
                folder, filename = result
                log_file_map[folder].append(filename)

        # --- Создание лог-файлов и отчета ---
        if log_file_map:
            print("\nСоздание лог-файлов...")
            await write_log_files(log_file_map)
        
        create_excel_report(report_data, base_output_dir, start_date, end_date, None if process_all_clients else partner_code)

    print("\nРабота завершена.")

if __name__ == "__main__":
    # Для Windows может понадобиться следующая строка
    # asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())