# main.py (версия 15, "Профессиональный подход")
import pyautogui
import pyperclip
import os
import time
from dotenv import load_dotenv
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from selenium.common.exceptions import (
    NoSuchElementException,
    StaleElementReferenceException,
    ElementClickInterceptedException,
    TimeoutException,
)
from selenium.webdriver.common.keys import Keys

load_dotenv()

LOGIN_EMAIL = os.getenv("VCHASNO_LOGIN_EMAIL")
LOGIN_PASSWORD = os.getenv("VCHASNO_LOGIN_PASSWORD")

START_URL = "https://edo.vchasno.ua/auth/start"
LOGIN_URL = "https://edo.vchasno.ua/auth/login"
SUCCESS_URL_PART = "/app/"

# --- ШАГ 1: ПРИМЕНЯЕМ ПРОФЕССИОНАЛЬНЫЕ ОПЦИИ CHROME ---
print("Конфигурируем браузер, чтобы он был менее заметен для защиты от ботов...")
chrome_options = Options()
# Отключаем "лишние" логи в консоли
chrome_options.add_argument("--log-level=3")
chrome_options.add_argument("--silent")
# Делаем Selenium менее заметным
chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
chrome_options.add_experimental_option('useAutomationExtension', False)
# Дополнительные опции для стабильности и скорости
chrome_options.add_argument("--no-sandbox")
chrome_options.add_argument("--disable-dev-shm-usage")
chrome_options.add_argument("--disable-gpu")
chrome_options.add_argument("--disable-extensions")
chrome_options.add_argument("--disable-popup-blocking")

driver = webdriver.Chrome(options=chrome_options)
driver.maximize_window()


# Пример использования функции для получения значения поля ввода
def get_input_value(driver, xpath):
    try:
        input_element = WebDriverWait(driver, 3).until(
            EC.element_to_be_clickable((By.XPATH, xpath))
        )
        return input_element.get_attribute("value")
    except Exception as e:
        # print(f"Error: {e}")
        return None


def get_active_element_name(driver):
    active_element = driver.execute_script("return document.activeElement;")
    element_name = active_element.get_attribute("name")
    element_placeholder = active_element.get_attribute("placeholder")
    element_id = active_element.get_attribute("id")
    print(f"Название поля: {element_name}")
    print(f"Placeholder поля: {element_placeholder}")
    print(f"ID поля: {element_id}")
    return element_name, element_placeholder, element_id

def ultimate_set_value(element, text):
    """
    Самый надежный метод, который имитирует всю цепочку событий.
    """
    print(f"    -> Выполняем полный цикл событий для '{text[:10]}...'")

    # Сначала очищаем поле
    element.clear()
    time.sleep(0.2)

    # Кликаем на элемент для фокуса
    driver.execute_script("arguments[0].click();", element)
    driver.execute_script("arguments[0].focus();", element)
    time.sleep(0.3)

    # Метод 1: Попробуем обычный send_keys
    try:
        element.send_keys(text)
        time.sleep(0.5)
        if element.get_attribute("value") == text:
            print(" -> Значение успешно установлено через send_keys.")
            return
    except:
        pass

    # Метод 2: Очищаем и устанавливаем через JS
    driver.execute_script("arguments[0].value = '';", element)
    driver.execute_script("arguments[0].value = arguments[1];", element, text)

    # Генерируем все необходимые события
    driver.execute_script("""
        var element = arguments[0];
        var text = arguments[1];

        // Событие focus
        element.dispatchEvent(new Event('focus', { bubbles: true }));

        // Событие input для каждого символа
        for (let i = 0; i < text.length; i++) {
            element.dispatchEvent(new Event('input', { bubbles: true }));
        }

        // Событие change
        element.dispatchEvent(new Event('change', { bubbles: true }));

        // Событие blur
        element.dispatchEvent(new Event('blur', { bubbles: true }));
    """, element, text)

    time.sleep(0.5)

    # Финальная проверка
    current_value = element.get_attribute("value")
    if current_value != text:
        print(f" -> Предупреждение: ожидали '{text}', получили '{current_value}'")
        # Попробуем еще раз через имитацию печати
        element.clear()
        time.sleep(0.2)
        element.click()
        time.sleep(0.2)
        for char in text:
            element.send_keys(char)
            time.sleep(0.05)
        time.sleep(0.3)

    print(" -> Значение успешно установлено и обработано сайтом.")

# --- Пример использования ---
try:
    wait = WebDriverWait(driver, 10)

    driver.get(START_URL)
    print(f"Открыли страницу: {driver.current_url}")

    # Попробуем разные селекторы для поля email
    email_input = None
    selectors = [
        "#login",
        "input[type='email']",
        "input[name='email']",
        "input[placeholder*='email']",
        "input[placeholder*='Email']",
        "input[placeholder*='пошта']",
        "input[placeholder*='логін']"
    ]

    for selector in selectors:
        try:
            email_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, selector)))
            print(f"Найдено поле email с селектором: {selector}")
            break
        except TimeoutException:
            continue

    if not email_input:
        raise Exception("Не удалось найти поле для ввода email")
    outer_html = email_input.get_attribute('outerHTML')
    if outer_html:
        print(f"Найдено поле email: {outer_html[:100]}...")
    else:
        print("Найдено поле email (outerHTML недоступен)")

    current_value = email_input.get_attribute('value') or ''
    print(f"Текущее значение поля: '{current_value}'")

    # Вызываем нашу функцию вместо send_keys
    ultimate_set_value(email_input, LOGIN_EMAIL)

    final_value = email_input.get_attribute('value') or ''
    print(f"После ввода значение поля: '{final_value}'")
    time.sleep(1)  # Дополнительная пауза

    continue_button = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "#login > div > div > button")))
    driver.execute_script("arguments[0].click();", continue_button)
    print(" -> Нажата кнопка 'Продовжити'.")

    # --- КЛЮЧЕВАЯ ПРОВЕРКА ---
    print(f"\nШаг 3: Проверяем переход на страницу пароля ({LOGIN_URL})...")
    try:
        wait.until(EC.url_to_be(LOGIN_URL))
        print(" -> Успех! Переход на страницу ввода пароля выполнен.")
    except TimeoutException:
        raise Exception(f"Не удалось перейти на страницу ввода пароля. Проверьте Email или скриншот на наличие CAPTCHA.")

    # --- ЭТАП 2: ВВОД ПАРОЛЯ ---
    print("\nШаг 4: Вводим Пароль...")
    password_input = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "#password")))
    # Вводим пароль
    ultimate_set_value(password_input, LOGIN_PASSWORD)

    submit_button = wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "button[type='submit']")))
    driver.execute_script("arguments[0].click();", submit_button)
    print(" -> Нажата кнопка 'Увійти'.")

    # --- ФИНАЛЬНАЯ ПРОВЕРКА ---
    print("\nШаг 5: Ждем результата авторизации...")
    wait.until(EC.url_contains(SUCCESS_URL_PART))
    
    print("\n[V] УСПЕХ! Авторизация прошла успешно.")
    print(f" -> Финальный URL: {driver.current_url}")
    
    print("\nСкрипт успешно завершен. Окно закроется через 5 секунд.")
    time.sleep(5)

except Exception as e:
    print(f"\n[X] ОШИБКА: {e}")
    driver.save_screenshot("error_screenshot.png")
    print(" -> Скриншот ошибки сохранен в файл 'error_screenshot.png'")
    time.sleep(10)

finally:
    print("Закрываем браузер...")
    driver.quit()