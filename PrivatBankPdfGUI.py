# -*- coding: utf-8 -*-
# pyinstaller --onefile --windowed --name PrivatBankDownloader PrivatBankPdfGUI.py
# https://gemini.google.com/app/2043f90d1b17f244
# Выгрузка квитанций ПриватБанка в PDF с GUI

import asyncio
import os
import threading
import queue
from datetime import datetime
from collections import defaultdict
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

import aiohttp
import aiofiles
from dotenv import load_dotenv
from dateutil.parser import parse

# Загружаем переменные окружения из файла .env
load_dotenv()

# --- КОНФИГУРАЦИЯ ---
TRANSACTIONS_URL = "https://acp.privatbank.ua/api/statements/transactions"
RECEIPT_URL = "https://acp.privatbank.ua/api/paysheets/print_receipt"
MAX_CONCURRENT_DOWNLOADS = 10

# --- ЛОГИКА API (Асинхронная часть) ---

class ApiClient:
    """Класс для инкапсуляции логики работы с API."""

    def __init__(self, token, log_queue):
        self.token = token
        self.log_queue = log_queue

    def log(self, message):
        """Отправляет сообщение в очередь для отображения в GUI."""
        self.log_queue.put(message)

    async def get_all_transactions(self, session, start_date, end_date):
        all_transactions = []
        follow_id = None
        page_num = 1
        headers = {
            'user-agent': 'Avtoklient',
            'token': self.token,
            'Content-Type': 'application/json;charset=utf-8'
        }
        self.log(f"Получение транзакций с {start_date} по {end_date}...")
        while True:
            params = {'startDate': start_date, 'endDate': end_date, 'limit': 500}
            if follow_id:
                params['followId'] = follow_id
            try:
                async with session.post(TRANSACTIONS_URL, params=params, headers=headers, timeout=30) as response:
                    response.raise_for_status()
                    data = await response.json()
                    transactions_on_page = data.get('transactions', [])
                    if transactions_on_page:
                        all_transactions.extend(transactions_on_page)
                        self.log(f"  - Получено {len(transactions_on_page)} транзакций со страницы {page_num}")
                    if data.get('exist_next_page'):
                        follow_id = data.get('next_page_id')
                        page_num += 1
                    else:
                        break
            except Exception as e:
                self.log(f"ОШИБКА при получении транзакций: {e}")
                break
        return all_transactions

    async def download_receipt(self, session, transaction, base_output_dir, semaphore):
        async with semaphore:
            try:
                doc_num = transaction.get('NUM_DOC', '0')
                doc_date_str = transaction.get('DAT_OD', 'UNKNOWN_DATE')
                doc_datetime = parse(doc_date_str, dayfirst=True)
                month_folder_name = doc_datetime.strftime('%Y%m')
                month_output_dir = os.path.join(base_output_dir, month_folder_name)
                os.makedirs(month_output_dir, exist_ok=True)
                doc_date_formatted = doc_datetime.strftime('%d %m %Y')
                filename = f"{doc_num} {doc_date_formatted}.pdf"
                filepath = os.path.join(month_output_dir, filename)

                if os.path.exists(filepath):
                    self.log(f"-> Файл {filename} уже существует, пропуск.")
                    return (month_output_dir, filename)

                self.log(f"-> Загрузка квитанции {filename}...")
                headers = {
                    'token': self.token,
                    'Content-Type': 'application/json',
                    'Accept': 'application/octet-stream'
                }
                payload = {
                    "transactions": [{"account": transaction.get('AUT_MY_ACC'), "reference": transaction.get('REF'), "refn": transaction.get('REFN')}],
                    "perPage": 1
                }
                async with session.post(RECEIPT_URL, json=payload, headers=headers, timeout=60) as response:
                    if response.status == 200:
                        content = await response.read()
                        async with aiofiles.open(filepath, 'wb') as f:
                            await f.write(content)
                        self.log(f"   - Файл {filename} успешно сохранен.")
                        return (month_output_dir, filename)
                    else:
                        error_text = await response.text()
                        self.log(f"   - ОШИБКА {response.status} при загрузке {filename}: {error_text[:200]}")
                        return None
            except Exception as e:
                self.log(f"   - КРИТИЧЕСКАЯ ОШИБКА при обработке транзакции {transaction.get('REF')}: {e}")
                return None

    async def write_log_files(self, file_map):
        for folder_path, filenames in file_map.items():
            if not filenames:
                continue
            month_str = os.path.basename(folder_path)
            log_filepath = os.path.join(folder_path, f"{month_str}.log")
            filenames.sort()
            log_content = ",".join(filenames)
            async with aiofiles.open(log_filepath, 'w', encoding='utf-8') as f:
                await f.write(log_content)
            self.log(f"Создан/обновлен лог-файл: {log_filepath}")

    async def main_async(self, partner_code, start_date, end_date):
        base_output_dir = f"receipts_{partner_code}"
        incoming_dir = os.path.join(base_output_dir, "входящие")
        outgoing_dir = os.path.join(base_output_dir, "исходящие")
        os.makedirs(incoming_dir, exist_ok=True)
        os.makedirs(outgoing_dir, exist_ok=True)
        self.log(f"Квитанции будут сохранены в папку: {base_output_dir}")

        semaphore = asyncio.Semaphore(MAX_CONCURRENT_DOWNLOADS)
        async with aiohttp.ClientSession() as session:
            all_transactions = await self.get_all_transactions(session, start_date, end_date)
            if not all_transactions:
                self.log("Транзакции за указанный период не найдены.")
                return

            incoming_transactions = [tx for tx in all_transactions if tx.get('TRANTYPE') == 'C' and tx.get('AUT_CNTR_CRF') == partner_code]
            outgoing_transactions = [tx for tx in all_transactions if tx.get('TRANTYPE') == 'D' and tx.get('AUT_CNTR_CRF') == partner_code]
            
            log_file_map = defaultdict(list)

            if incoming_transactions:
                self.log(f"\nНайдено {len(incoming_transactions)} входящих транзакций. Начало загрузки...")
                tasks_in = [self.download_receipt(session, tx, incoming_dir, semaphore) for tx in incoming_transactions]
                results_in = await asyncio.gather(*tasks_in)
                for result in results_in:
                    if result: log_file_map[result[0]].append(result[1])
            else:
                self.log(f"\nВходящих транзакций от контрагента с кодом {partner_code} не найдено.")

            if outgoing_transactions:
                self.log(f"\nНайдено {len(outgoing_transactions)} исходящих транзакций. Начало загрузки...")
                tasks_out = [self.download_receipt(session, tx, outgoing_dir, semaphore) for tx in outgoing_transactions]
                results_out = await asyncio.gather(*tasks_out)
                for result in results_out:
                    if result: log_file_map[result[0]].append(result[1])
            else:
                self.log(f"\nИсходящих транзакций для контрагента с кодом {partner_code} не найдено.")
            
            if log_file_map:
                self.log("\nСоздание лог-файлов...")
                await self.write_log_files(log_file_map)

        self.log("\nРабота завершена.")


# --- ГРАФИЧЕСКИЙ ИНТЕРФЕЙС (Tkinter) ---

class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Загрузчик квитанций ПриватБанка")
        self.root.geometry("700x550")
        self.root.minsize(600, 450)

        self.log_queue = queue.Queue()
        self.thread = None

        # Стили
        style = ttk.Style(self.root)
        style.configure("TLabel", padding=5, font=('Helvetica', 10))
        style.configure("TEntry", padding=5)
        style.configure("TButton", padding=5, font=('Helvetica', 10, 'bold'))

        # Фрейм для полей ввода
        input_frame = ttk.Frame(root, padding="10")
        input_frame.pack(fill=tk.X, padx=10, pady=5)

        # Поля для ввода данных
        ttk.Label(input_frame, text="Код контрагента (ЕГРПОУ/ИНН):").grid(row=0, column=0, sticky=tk.W)
        self.partner_code_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.partner_code_var, width=40).grid(row=0, column=1, sticky=tk.EW)

        # Поле для токена удалено из интерфейса для безопасности
        
        ttk.Label(input_frame, text="Дата начала (ДД-ММ-ГГГГ):").grid(row=2, column=0, sticky=tk.W)
        self.start_date_var = tk.StringVar(value="01-01-2024")
        ttk.Entry(input_frame, textvariable=self.start_date_var, width=40).grid(row=2, column=1, sticky=tk.EW)

        ttk.Label(input_frame, text="Дата конца (ДД-ММ-ГГГГ):").grid(row=3, column=0, sticky=tk.W)
        self.end_date_var = tk.StringVar(value=datetime.today().strftime("%d-%m-%Y"))
        ttk.Entry(input_frame, textvariable=self.end_date_var, width=40).grid(row=3, column=1, sticky=tk.EW)
        
        input_frame.columnconfigure(1, weight=1)

        # Кнопка запуска
        self.run_button = ttk.Button(root, text="Скачать квитанции", command=self.start_download)
        self.run_button.pack(fill=tk.X, padx=20, pady=10)

        # Окно для вывода логов
        log_frame = ttk.Frame(root, padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        self.log_area = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=('Courier New', 9))
        self.log_area.pack(fill=tk.BOTH, expand=True)
        self.log_area.configure(state='disabled')

        # Статус-бар
        self.status_var = tk.StringVar(value="Готово")
        ttk.Label(root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W, padding=5).pack(side=tk.BOTTOM, fill=tk.X)

        self.process_queue()

    def start_download(self):
        """Запускает процесс загрузки в отдельном потоке."""
        partner_code = self.partner_code_var.get().strip()
        token = os.getenv("PB_PPK_TOKEN")
        start_date = self.start_date_var.get().strip()
        end_date = self.end_date_var.get().strip()

        if not all([partner_code, start_date, end_date]):
            messagebox.showerror("Ошибка", "Код контрагента и обе даты должны быть заполнены.")
            return
        
        if not token:
            messagebox.showerror("Ошибка", "Токен не найден. Проверьте ваш .env файл и имя переменной (PB_PPK_TOKEN).")
            return

        # Блокируем кнопку на время работы
        self.run_button.config(state="disabled")
        self.status_var.set("Загрузка...")
        self.clear_log()

        # Запускаем асинхронную логику в отдельном потоке, чтобы не блокировать GUI
        self.thread = threading.Thread(
            target=self.run_async_downloader,
            args=(token, partner_code, start_date, end_date),
            daemon=True
        )
        self.thread.start()

    def run_async_downloader(self, token, partner_code, start_date, end_date):
        """Целевая функция для потока, запускает asyncio event loop."""
        try:
            client = ApiClient(token, self.log_queue)
            asyncio.run(client.main_async(partner_code, start_date, end_date))
        except Exception as e:
            self.log_queue.put(f"КРИТИЧЕСКАЯ ОШИБКА В ПОТОКЕ: {e}")
        finally:
            # Отправляем сигнал о завершении работы
            self.log_queue.put(None)

    def process_queue(self):
        """Обрабатывает очередь сообщений из фонового потока."""
        try:
            while True:
                msg = self.log_queue.get_nowait()
                if msg is None:
                    # Сигнал завершения
                    self.run_button.config(state="normal")
                    self.status_var.set("Готово")
                    messagebox.showinfo("Завершено", "Загрузка квитанций завершена.")
                    break
                else:
                    self.log_to_widget(msg)
        except queue.Empty:
            pass
        
        self.root.after(100, self.process_queue)

    def log_to_widget(self, message):
        """Добавляет сообщение в текстовое поле лога."""
        self.log_area.configure(state='normal')
        self.log_area.insert(tk.END, message + '\n')
        self.log_area.configure(state='disabled')
        self.log_area.yview(tk.END) # Автопрокрутка вниз

    def clear_log(self):
        self.log_area.configure(state='normal')
        self.log_area.delete('1.0', tk.END)
        self.log_area.configure(state='disabled')


if __name__ == "__main__":
    root = tk.Tk()
    app = App(root)
    root.mainloop()
